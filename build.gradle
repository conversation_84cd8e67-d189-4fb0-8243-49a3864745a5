plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.1'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.sportal365'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	// Use Maven Central for resolving dependencies.
	mavenCentral()
	maven {
		url "https://reposilite.internal.sportal365.com/private"
		credentials {
			username "viewer"
			password "VsqA4Qh8ak1lodqD9cOpd/ewyWYlZ/StiyMHnnyL5EXFZGJYzC+rjzNXmPfzuQbk"
		}
	}
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-webflux'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'

	// Springdoc
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

	//Mapstruct
	implementation 'org.mapstruct:mapstruct:1.5.5.Final'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
	annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

	//sportal365
	implementation "com.sportal365:common:57"
	implementation "com.sportal365:configurationclient:18"

	//Sentry
	implementation 'io.sentry:sentry-spring-boot-starter-jakarta:7.9.0'
	implementation 'io.sentry:sentry-logback:7.9.0'
}

tasks.named('test') {
	useJUnitPlatform()
}

bootRun {
	if (project.hasProperty('debug')) {
		jvmArgs "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"

		doFirst {
			copy {
				from 'scripts/pre-commit.sh'
				into '.git/hooks'
				rename('pre-commit.sh', 'pre-commit')
				filePermissions {
					user {
						read = true
						write = true
						execute = true
					}
					group {
						read = true
						execute = true
					}
				}
			}
		}
	}
}
