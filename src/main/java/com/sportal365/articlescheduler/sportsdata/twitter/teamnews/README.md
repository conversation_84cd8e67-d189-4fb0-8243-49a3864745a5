# X.AI Team News Integration

This package implements the X.AI Grok API integration for retrieving comprehensive real-time team news, manager quotes, and player quotes for football matches in the Article Scheduler Service.

## Overview

The team news integration follows the established sportsdata package patterns and integrates seamlessly with the `MatchDetailPipelineService` to enrich match details with current team information from X/Twitter and web sources. The implementation uses a single-service architecture where `TeamNewsService` directly implements `MatchDetailEnrichService` for optimal simplicity and maintainability.

### Key Features
- **Professional Sports Journalism**: Generates comprehensive match coverage with tactical analysis, injury reports, and lineup predictions
- **Comprehensive Event Details**: Extracts and utilizes all available match metadata (venue, referee, round, country, sport)
- **Multi-language Support**: Dynamic language selection from Schedule configuration
- **Robust Field Extraction**: Graceful handling of missing data with meaningful fallbacks
- **Structured Content**: Professional Markdown formatting with team-specific sections

## Architecture

### Package Structure
```
teamnews/
├── client/
│   ├── configuration/
│   │   └── XaiApiClientConfiguration.java
│   └── XaiApiClient.java
├── constants/
│   └── TeamNewsConstants.java
├── mapper/
│   ├── TeamNewsMapper.java
│   └── TeamNewsResponseMapper.java
├── model/
│   ├── TeamNewsData.java
│   ├── XaiApiRequest.java
│   └── XaiApiResponse.java
└── service/
    └── TeamNewsService.java
```

### Key Components

#### 1. XaiApiClient
- Extends `BaseApiClient` following established patterns
- Handles HTTP communication with X.AI API
- Implements proper error handling and reactive logging operations
- Includes `doOnSubscribe`, `doOnSuccess`, and `doOnError` logging following `StatisticsApiClient` pattern

#### 2. TeamNewsMapper
- **Comprehensive Field Extraction**: Maps all `MatchDetails` properties to API request
- **17-Parameter Template**: Supports venue, referee, round, country, sport, language, and team details
- **Robust Null Handling**: Graceful fallbacks for missing data with meaningful defaults
- **Professional Prompt Construction**: Generates sports journalism quality requests
- **Dynamic Date Formatting**: Calculates date ranges and formats for optimal search results

#### 3. TeamNewsResponseMapper
- Parses Markdown-formatted API responses using regex patterns
- Extracts team news categorized by injuries, suspensions, key players, and returnees
- Parses manager and player quotes with proper team attribution
- Maps to `MatchDetails.TeamNews` and `MatchDetails.Quotes` structures

#### 4. TeamNewsService (Single Service Architecture)
- **Primary Component**: Implements `MatchDetailEnrichService` interface directly
- **Pipeline Integration**: Seamlessly integrates with `MatchDetailPipelineService`
- **API Communication**: Handles X.AI API calls through `XaiApiClient`
- **Data Enrichment**: Processes and maps API responses to domain objects
- **Error Handling**: Graceful error handling with fallback to original match details
- **Logging**: Comprehensive logging at both service and client levels

## Configuration

### Environment Variables
```bash
XAI_API_BASE_URL=https://api.x.ai
XAI_API_KEY=xai-your-api-key-here
```

### Application Properties
```properties
xai.api.base.url=${XAI_API_BASE_URL}
xai.api.key=${XAI_API_KEY}
```

## API Integration Details

### Enhanced Request Structure
The integration uses the X.AI chat completions endpoint with live search capabilities. Requests are dynamically constructed from comprehensive `MatchDetails` and `Schedule` data:

#### Professional Sports Journalism Prompt
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a sports news assistant with access to live X/Twitter search capabilities. When searching for match news, prioritize posts from verified sports accounts, official team accounts, and credible football journalists. Always indicate the source and recency of information found through live search. Format your response in clean Markdown with proper headings, bullet points, and clear paragraph breaks."
    },
    {
      "role": "user",
      "content": "Please provide comprehensive sports journalism coverage for the upcoming Football match between Barcelona and Real Madrid in the La Liga tournament scheduled for 15.06.2025. The event will take place at Camp Nou with Antonio Mateu Lahoz officiating as referee. This is a Matchday 15 round match in Spain for the sport of Football. Please respond in english language.\n\nProvide detailed coverage including:\n- Latest team news, injury updates, and squad changes\n- Manager quotes with tactical insights and match preparation comments\n- Player quotes, interviews, and pre-match statements\n- Key storylines, head-to-head records, and match significance\n- Lineup predictions and formation analysis\n- Recent form analysis and performance statistics\n\nFormat your response using clean, professional Markdown structure:\n\n## Team News\n### Barcelona News\n### Real Madrid News\n\n## Manager Quotes\n### Barcelona Manager\n### Real Madrid Manager\n\n## Player Quotes\n### Barcelona Players\n### Real Madrid Players\n\n## Match Preview\n### Key Storylines\n### Tactical Analysis\n### Predictions\n\nUse proper Markdown formatting with clear headings, bullet points, bold text for emphasis, and include source citations where available. Ensure all content is factual, recent, and relevant."
    }
  ],
  "model": "grok-3-latest",
  "stream": false,
  "temperature": 0,
  "max_tokens": 1500,
  "search_parameters": {
    "mode": "on",
    "sources": [
      {"type": "x", "x_handles": ["UEFA", "FIFAcom"]},
      {"type": "web"},
      {"type": "news"}
    ],
    "from_date": "2024-12-11",  // Dynamically calculated (last 30 days)
    "return_citations": true,
    "max_search_results": 20
  }
}
```

#### Comprehensive Field Mapping (17 Parameters)
The prompt template dynamically incorporates all available match details:

1. **Sport** - `matchDetails.getSport()` → fallback: "Football"
2. **Home Team** - `matchDetails.getHomeTeam()` → fallback: "Home Team"
3. **Away Team** - `matchDetails.getAwayTeam()` → fallback: "Away Team"
4. **Tournament** - `matchDetails.getTournamentName()` → fallback: "Tournament"
5. **Date** - `formatMatchDate()` → fallback: "Unknown Date"
6. **Venue** - `matchDetails.getVenue()` → fallback: "TBD Venue"
7. **Referee** - `matchDetails.getReferee()` → fallback: "TBD Referee"
8. **Round** - `matchDetails.getRound()` → fallback: "Regular Round"
9. **Country** - `matchDetails.getCountry()` → fallback: "International"
10. **Sport** - Repeated for contextual clarity
11. **Language** - `schedule.getArticleLanguage()` → fallback: "english"
12-17. **Team Names** - Repeated for section-specific formatting

### Enhanced Response Processing
The API returns professionally structured Markdown content with comprehensive coverage:

#### Content Structure
- **Team News**: Organized by home/away team with injury updates, squad changes, and tactical insights
- **Manager Quotes**: Team-specific sections with tactical preparation and match analysis
- **Player Quotes**: Interviews and pre-match statements organized by team
- **Match Preview**: Key storylines, tactical analysis, and predictions
- **Performance Statistics**: Recent form analysis and head-to-head records

#### Professional Content Requirements
- **Tactical Analysis**: Formation analysis and strategic insights
- **Injury Reports**: Latest fitness updates and squad availability
- **Lineup Predictions**: Expected formations and key player selections
- **Match Significance**: Historical context and tournament implications
- **Recent Form**: Performance statistics and momentum analysis

### Data Mapping
The response is mapped to existing `MatchDetails` structures:

#### TeamNews Structure
```java
MatchDetails.TeamNews {
    homeTeam: {
        injuries: List<String>
        suspended: List<String>
        keyPlayers: List<String>
        returnees: List<String>
    }
    awayTeam: { ... }
}
```

#### Quotes Structure
```java
MatchDetails.Quotes {
    homeTeam: ManagerQuote
    awayTeam: {
        manager: ManagerQuote
        player: PlayerQuote
    }
}
```

## Pipeline Integration

The `TeamNewsService` is integrated directly into the `MatchDetailPipelineService` for football matches as a `MatchDetailEnrichService`:

```java
// MatchDetailPipelineService.java
public List<MatchDetailEnrichService> getPipeline(Schedule schedule) {
    if (schedule.getSport() == SportEnum.FOOTBALL) {
        return List.of(
            sportSearchService,
            footballService,
            standingsService,
            playoffsDataService,
            formGuideService,
            statisticsService,
            teamNewsService  // ✅ Single service implementation
        );
    }
    return Collections.emptyList();
}
```

### Service Architecture Benefits
- **Single Responsibility**: One service handles all team news concerns
- **Simplified Dependencies**: Fewer components to inject and manage
- **Easier Testing**: Single service to mock and verify
- **Consistent Patterns**: Follows other pipeline service implementations
- **Reduced Complexity**: Eliminates unnecessary service layer separation

## Error Handling

The service implements comprehensive error handling at multiple levels:

### Service Level (`TeamNewsService`)
- **API Failures**: Returns original `MatchDetails` without team news to prevent pipeline breaks
- **Null Data**: Gracefully handles empty or null API responses
- **Exception Handling**: Catches all exceptions and logs errors while maintaining pipeline flow
- **Fallback Strategy**: Always returns valid `MatchDetails` object

### Client Level (`XaiApiClient`)
- **Network Issues**: Handled by `BaseApiClient` with proper logging and retries
- **HTTP Errors**: Reactive error handling with detailed logging
- **Rate Limiting**: Managed through reactive patterns and proper backpressure
- **Timeout Handling**: Configurable timeouts with graceful degradation

### Logging Strategy
- **Client Logging**: `doOnSubscribe`, `doOnSuccess`, `doOnError` following `StatisticsApiClient` pattern
- **Service Logging**: High-level operation logging with context
- **Error Tracking**: Comprehensive error logging with match context and stack traces

## Monitoring and Logging

### Token Usage Tracking
The service tracks detailed API usage metrics for cost monitoring and optimization:

```java
TeamNewsData.TokenUsage {
    promptTokens: int        // Input tokens consumed
    completionTokens: int    // Output tokens generated
    totalTokens: int         // Total tokens used
}
```

### Source Attribution and Citations
- **Live Search Citations**: All source URLs from live search results are preserved
- **Source Count Tracking**: Number of sources used is tracked and logged
- **Verification Support**: Source URLs available for content verification and auditing
- **Attribution Compliance**: Proper source attribution for content licensing

### Reactive Logging Pattern
Following the established `StatisticsApiClient` pattern:

```java
// XaiApiClient logging
.doOnSubscribe(subscription -> log.debug("Calling X.AI API for team news: {}", logContext))
.doOnSuccess(response -> log.debug("X.AI API call completed. Tokens used: {}, Sources used: {}, Context: {}", ...))
.doOnError(error -> log.error("X.AI API call failed: {}", error.getMessage()))

// TeamNewsService logging
.doOnSuccess(teamNews -> log.debug("Successfully retrieved team news for match: {}", matchDetails.getMatchId()))
.doOnError(error -> log.error("Error retrieving team news for match: {}. Error: {}", ...))
```

## Testing

### Comprehensive Test Suite (16 Tests Total)

#### Unit Tests
- **`TeamNewsMapperTest`** (6 tests): Enhanced field extraction, comprehensive null handling, language support
- **`TeamNewsResponseMapperTest`** (4 tests): Markdown parsing, quote extraction, error scenarios
- **`TeamNewsServiceTest`** (5 tests): Service integration, enrichment logic, error handling
- **`TeamNewsEnhancementTest`** (1 test): Professional prompt validation and field mapping

#### Enhanced Test Coverage
- ✅ **Comprehensive Field Extraction**: All 17 parameters from MatchDetails and Schedule
- ✅ **Professional Prompt Validation**: Sports journalism quality and structure verification
- ✅ **Robust Null Handling**: Fallback values for venue, referee, round, country, sport
- ✅ **Language Support**: Custom language testing and default fallback validation
- ✅ **Edge Case Scenarios**: Partial null fields, whitespace handling, empty strings
- ✅ **Response Parsing**: Markdown content extraction and domain object mapping
- ✅ **Error Handling**: API failures, null responses, parsing errors
- ✅ **Integration Logic**: Complete enrichment workflow with mocked dependencies

#### Enhanced Testing Strategy
```java
// Comprehensive test structure with enhanced field validation
@ExtendWith(MockitoExtension.class)
class TeamNewsMapperTest {

    @Test
    void mapToXaiRequest_ShouldCreateValidRequest() {
        // Tests all 17 parameters: sport, teams, tournament, date, venue, referee, round, country, language
        MatchDetails matchDetails = MatchDetails.builder()
            .homeTeam("Barcelona").awayTeam("Real Madrid")
            .venue("Camp Nou").referee("Antonio Mateu Lahoz")
            .round("Matchday 15").country("Spain").sport("Football")
            .build();

        // Validates professional prompt structure and comprehensive field extraction
    }

    @Test
    void mapToXaiRequest_ShouldHandlePartialNullFields() {
        // Tests fallback values: "TBD Venue", "TBD Referee", "Regular Round", etc.
    }
}
```

### Integration Testing
- **Real API Testing**: Service can be tested with actual X.AI API calls
- **Pipeline Testing**: Integration with `MatchDetailPipelineService`
- **End-to-End Testing**: Complete workflow from `MatchDetails` to enriched output

## Usage Example

### Enhanced Service Usage
```java
@Autowired
private TeamNewsService teamNewsService;

public MatchDetails enrichMatchWithComprehensiveTeamNews(MatchDetails matchDetails, Schedule schedule) {
    // Direct enrichment call with comprehensive field extraction
    MatchDetails enrichedMatch = teamNewsService.enrich(matchDetails, schedule);

    // Access professionally structured data
    MatchDetails.TeamNews teamNews = enrichedMatch.getTeamNews();
    MatchDetails.Quotes quotes = enrichedMatch.getQuotes();

    // Example: Access team-specific injury reports
    List<String> homeTeamInjuries = teamNews.getHomeTeam().getInjuries();
    List<String> awayTeamSuspensions = teamNews.getAwayTeam().getSuspended();

    // Example: Access manager tactical insights
    String homeManagerQuote = quotes.getHomeTeam().getQuote();
    String awayManagerQuote = quotes.getAwayTeam().getManager().getQuote();

    return enrichedMatch;
}
```

### Pipeline Integration (Automatic)
```java
// TeamNewsService is automatically called as part of the pipeline
@Autowired
private MatchDetailPipelineService pipelineService;

public MatchDetails processMatch(MatchDetails matchDetails, Schedule schedule) {
    // TeamNewsService.enrich() is called automatically in the pipeline
    return pipelineService.enrich(matchDetails, schedule);
}
```

### Professional Content Access Examples
```java
// Comprehensive team news with enhanced categorization
MatchDetails.TeamNews teamNews = enrichedMatch.getTeamNews();

// Injury reports and fitness updates
List<String> homeInjuries = teamNews.getHomeTeam().getInjuries();
List<String> awayInjuries = teamNews.getAwayTeam().getInjuries();

// Suspension and disciplinary information
List<String> homeSuspensions = teamNews.getHomeTeam().getSuspended();
List<String> awaySuspensions = teamNews.getAwayTeam().getSuspended();

// Key player information and returnees
List<String> homeKeyPlayers = teamNews.getHomeTeam().getKeyPlayers();
List<String> awayReturnees = teamNews.getAwayTeam().getReturnees();

// Professional quotes with tactical insights
MatchDetails.Quotes quotes = enrichedMatch.getQuotes();

// Manager tactical analysis and preparation comments
String homeManagerQuote = quotes.getHomeTeam().getQuote();
String homeManagerName = quotes.getHomeTeam().getName();

// Player interviews and pre-match statements
String awayPlayerQuote = quotes.getAwayTeam().getPlayer().getQuote();
String awayPlayerName = quotes.getAwayTeam().getPlayer().getName();
```

## Dependencies

- Spring WebFlux for reactive HTTP client
- Jackson for JSON serialization
- Lombok for boilerplate reduction
- JUnit 5 for testing

## Future Enhancements

### Professional Content Improvements
1. **Advanced Tactical Analysis**: Enhanced formation analysis and strategic insights
2. **Historical Context Integration**: Head-to-head records and historical significance
3. **Performance Metrics**: Advanced statistics and performance indicators
4. **Sentiment Analysis**: Tone analysis of quotes and news content

### Performance Optimizations
5. **Response Caching**: Implement Redis caching for API responses to reduce costs
6. **Rate Limiting**: Add client-side rate limiting to respect API quotas
7. **Request Batching**: Batch multiple match requests for efficiency
8. **Async Processing**: Non-blocking enrichment for high-volume scenarios

### Feature Enhancements
9. **Enhanced Field Extraction**: Additional MatchDetails properties (weather, attendance, etc.)
10. **Fallback Sources**: Integrate additional news sources (Twitter API v2, RSS feeds)
11. **Real-time Updates**: WebSocket integration for live team news updates
12. **Advanced Language Support**: Regional dialects and specialized sports terminology
13. **Team-specific Handles**: Dynamic X/Twitter handle discovery per team

### Monitoring & Analytics
14. **Content Quality Scoring**: AI-powered content quality assessment
15. **Cost Analytics**: Enhanced token usage and cost tracking with dashboards
16. **Performance Monitoring**: API response time and success rate metrics
17. **A/B Testing**: Compare different prompt strategies for better results

### Integration Improvements
18. **Webhook Support**: Real-time notifications for breaking team news
19. **Content Filtering**: Advanced filtering for relevant vs irrelevant news
20. **Source Prioritization**: Dynamic source weighting based on reliability
21. **Historical Data**: Archive and analyze team news trends over time

## Architecture Summary

The X.AI team news integration provides a **production-ready, professional sports journalism platform** that:

### Core Capabilities
- ✅ **Professional Sports Journalism**: Generates comprehensive match coverage with tactical analysis
- ✅ **Comprehensive Field Extraction**: Utilizes all 17 parameters from MatchDetails and Schedule
- ✅ **Robust Error Handling**: Graceful fallbacks for missing data with meaningful defaults
- ✅ **Multi-language Support**: Dynamic language selection with international coverage
- ✅ **Structured Content**: Professional Markdown formatting with team-specific sections

### Technical Excellence
- ✅ **Single-Service Architecture**: Optimal maintainability with `TeamNewsService` implementing `MatchDetailEnrichService`
- ✅ **Seamless Pipeline Integration**: Follows established sportsdata package patterns
- ✅ **Comprehensive Testing**: 16 passing tests with enhanced field validation
- ✅ **Reactive Programming**: WebFlux patterns with proper error handling and logging
- ✅ **Clean Code Structure**: Clear separation between HTTP, mapping, and business logic

### Production Features
- ✅ **Real-time Data Integration**: Live X/Twitter search with web and news sources
- ✅ **Professional Content Requirements**: Tactical insights, injury reports, lineup predictions
- ✅ **Source Attribution**: Comprehensive citation tracking and verification
- ✅ **Token Usage Monitoring**: Cost tracking and performance analytics
- ✅ **Scalable Architecture**: Ready for high-volume deployment and continuous enhancement

**Ready for professional sports journalism deployment with comprehensive match coverage!** ⚽🚀
