package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.PromptTemplateBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsPromptTemplate;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.XaiRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.ResponseToDomainMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsDataMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentCleaningStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentParsingStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.JsonContentConverter;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.MarkdownContentCleaner;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.MarkdownContentProcessor;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service.DateFormattingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Configuration class for Team News components.
 * Resolves circular dependencies and provides proper bean wiring.
 */
@Configuration
public class TeamNewsConfiguration {
    
    @Bean
    @Primary
    public ObjectMapper teamNewsObjectMapper() {
        return new ObjectMapper();
    }
    
    @Bean
    @Primary
    public ContentParsingStrategy contentParsingStrategy() {
        return new MarkdownContentProcessor();
    }
    
    @Bean
    @Primary
    public ContentCleaningStrategy contentCleaningStrategy() {
        return new MarkdownContentCleaner();
    }
    
    @Bean
    public JsonContentConverter jsonContentConverter(
            ObjectMapper teamNewsObjectMapper,
            ContentParsingStrategy contentParsingStrategy,
            ContentCleaningStrategy contentCleaningStrategy) {
        return new JsonContentConverter(teamNewsObjectMapper, contentParsingStrategy, contentCleaningStrategy);
    }
    
    @Bean
    @Primary
    public ResponseToDomainMapper responseToDomainMapper(
            ContentParsingStrategy contentParsingStrategy,
            ContentCleaningStrategy contentCleaningStrategy) {
        return new TeamNewsDataMapper(contentParsingStrategy, contentCleaningStrategy);
    }
    
    @Bean
    @Primary
    public PromptTemplateBuilder promptTemplateBuilder(DateFormattingService dateFormattingService) {
        return new TeamNewsPromptTemplate(dateFormattingService);
    }
    
    @Bean
    @Primary
    public XaiRequestBuilder xaiRequestBuilder() {
        return new TeamNewsRequestBuilder();
    }
}
