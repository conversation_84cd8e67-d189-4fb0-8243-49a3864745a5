package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.PromptTemplateBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsPromptTemplate;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.TeamNewsRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.builder.XaiRequestBuilder;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.chain.ContentProcessingPipeline;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.config.TeamNewsConfigurationProperties;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.factory.MapperFactory;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.factory.TeamNewsMapperFactory;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.ResponseToDomainMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsDataMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring.MetricsCollector;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring.TeamNewsMetricsCollector;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentCleaningStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.ContentParsingStrategy;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.JsonContentConverter;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.MarkdownContentCleaner;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.MarkdownContentProcessor;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.resilience.CircuitBreaker;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.resilience.TeamNewsCircuitBreaker;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service.DateFormattingService;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.strategy.ContentProcessingStrategySelector;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * Configuration class for Team News components.
 * Resolves circular dependencies and provides proper bean wiring.
 */
@Configuration
public class TeamNewsConfiguration {
    
    @Bean
    @Primary
    public ObjectMapper teamNewsObjectMapper() {
        return new ObjectMapper();
    }
    
    @Bean
    @Primary
    public ContentParsingStrategy contentParsingStrategy() {
        return new MarkdownContentProcessor();
    }
    
    @Bean
    @Primary
    public ContentCleaningStrategy contentCleaningStrategy() {
        return new MarkdownContentCleaner();
    }
    
    @Bean
    public JsonContentConverter jsonContentConverter(
            ObjectMapper teamNewsObjectMapper,
            ContentParsingStrategy contentParsingStrategy,
            ContentCleaningStrategy contentCleaningStrategy) {
        return new JsonContentConverter(teamNewsObjectMapper, contentParsingStrategy, contentCleaningStrategy);
    }
    
    @Bean
    @Primary
    public ResponseToDomainMapper responseToDomainMapper(
            ContentParsingStrategy contentParsingStrategy,
            ContentCleaningStrategy contentCleaningStrategy) {
        return new TeamNewsDataMapper(contentParsingStrategy, contentCleaningStrategy);
    }
    
    @Bean
    @Primary
    public PromptTemplateBuilder promptTemplateBuilder(DateFormattingService dateFormattingService) {
        return new TeamNewsPromptTemplate(dateFormattingService);
    }
    
    @Bean
    @Primary
    public XaiRequestBuilder xaiRequestBuilder() {
        return new TeamNewsRequestBuilder();
    }

    @Bean
    public ContentProcessingStrategySelector contentProcessingStrategySelector(
            List<ContentParsingStrategy> parsingStrategies,
            List<ContentCleaningStrategy> cleaningStrategies) {
        return new ContentProcessingStrategySelector(parsingStrategies, cleaningStrategies);
    }

    @Bean
    @Primary
    public MapperFactory mapperFactory(
            ContentParsingStrategy contentParsingStrategy,
            ContentCleaningStrategy contentCleaningStrategy,
            DateFormattingService dateFormattingService) {
        return new TeamNewsMapperFactory(contentParsingStrategy, contentCleaningStrategy, dateFormattingService);
    }
}
