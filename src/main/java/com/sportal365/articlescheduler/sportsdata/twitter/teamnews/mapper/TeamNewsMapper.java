package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiRequest;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.XaiApiResponse;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.constants.TeamNewsConstants;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TeamNewsMapper {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy");

    public XaiApiRequest mapToXaiRequest(MatchDetails matchDetails, Schedule schedule) {
        // Extract basic match information
        String homeTeam = extractValue(matchDetails.getHomeTeam(), "Home Team");
        String awayTeam = extractValue(matchDetails.getAwayTeam(), "Away Team");
        String tournament = extractValue(matchDetails.getTournamentName(), "Tournament");
        String matchDate = formatMatchDate(matchDetails.getDate(), matchDetails.getTime());
        String venue = extractValue(matchDetails.getVenue(), "TBD Venue");
        String referee = extractValue(matchDetails.getReferee(), "TBD Referee");
        String round = extractValue(matchDetails.getRound(), "Regular Round");
        String country = extractValue(matchDetails.getCountry(), "International");
        String sport = extractValue(matchDetails.getSport(), "Football");
        String language = schedule != null && schedule.getArticleLanguage() != null ?
                         schedule.getArticleLanguage() : "english";

        String userPrompt = String.format(
                TeamNewsConstants.USER_PROMPT_TEMPLATE,
                sport,           // 1. Sport (%s)
                homeTeam,        // 2. Home team (%s)
                awayTeam,        // 3. Away team (%s)
                tournament,      // 4. Tournament (%s)
                matchDate,       // 5. Date (%s)
                venue,           // 6. Venue (%s)
                referee,         // 7. Referee (%s)
                round,           // 8. Round (%s)
                country,         // 9. Country (%s)
                sport,           // 10. Sport (%s) - for "sport of %s"
                language,        // 11. Language (%s)
                homeTeam,        // 12. Home team (%s) - for "### %s News"
                awayTeam,        // 13. Away team (%s) - for "### %s News"
                homeTeam,        // 14. Home team (%s) - for "### %s Manager"
                awayTeam,        // 15. Away team (%s) - for "### %s Manager"
                homeTeam,        // 16. Home team (%s) - for "### %s Players"
                awayTeam         // 17. Away team (%s) - for "### %s Players"
        );

        return XaiApiRequest.builder()
                .messages(List.of(
                        XaiApiRequest.Message.builder()
                                .role(TeamNewsConstants.SYSTEM_ROLE)
                                .content(TeamNewsConstants.SYSTEM_PROMPT)
                                .build(),
                        XaiApiRequest.Message.builder()
                                .role(TeamNewsConstants.USER_ROLE)
                                .content(userPrompt)
                                .build()
                ))
                .model(TeamNewsConstants.XAI_MODEL)
                .stream(TeamNewsConstants.DEFAULT_STREAM)
                .temperature(TeamNewsConstants.DEFAULT_TEMPERATURE)
                .maxTokens(TeamNewsConstants.DEFAULT_MAX_TOKENS)
                .searchParameters(buildSearchParameters())
                .build();
    }

    public TeamNewsData mapToTeamNewsData(MatchDetails matchDetails, XaiApiResponse response) {
        String content = extractContentFromResponse(response);

        return TeamNewsData.builder()
                .matchId(matchDetails.getMatchId())
                .homeTeam(matchDetails.getHomeTeam())
                .awayTeam(matchDetails.getAwayTeam())
                .competition(matchDetails.getTournamentName())
                .matchDate(formatMatchDate(matchDetails.getDate(), matchDetails.getTime()))
                .markdownContent(content)
                .citations(response != null ? response.getCitations() : null)
                .tokenUsage(mapTokenUsage(response != null ? response.getUsage() : null))
                .sourcesUsed(response != null && response.getUsage() != null ? response.getUsage().getNumSourcesUsed() : 0)
                .build();
    }



    private XaiApiRequest.SearchParameters buildSearchParameters() {

        return XaiApiRequest.SearchParameters.builder()
                .mode(TeamNewsConstants.SEARCH_MODE_ON)
                .sources(List.of(
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_X)
                                .xHandles(TeamNewsConstants.DEFAULT_X_HANDLES)
                                .build(),
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_WEB)
                                .build(),
                        XaiApiRequest.SearchParameters.Source.builder()
                                .type(TeamNewsConstants.SOURCE_TYPE_NEWS)
                                .build()
                ))
                .returnCitations(TeamNewsConstants.DEFAULT_RETURN_CITATIONS)
                .maxSearchResults(TeamNewsConstants.DEFAULT_MAX_SEARCH_RESULTS)
                .build();
    }

    private String extractValue(String value, String fallback) {
        return value != null && !value.trim().isEmpty() ? value.trim() : fallback;
    }

    private String formatMatchDate(String date, String time) {
        try {
            if (date != null && !date.isEmpty()) {
                LocalDate localDate = LocalDate.parse(date);
                return localDate.format(DATE_FORMATTER);
            }
        } catch (Exception e) {
            // Log error and return fallback
        }
        return "Unknown Date";
    }

    private String extractContentFromResponse(XaiApiResponse response) {
        if (response != null &&
            response.getChoices() != null &&
            !response.getChoices().isEmpty() &&
            response.getChoices().get(0).getMessage() != null) {

            String content = response.getChoices().get(0).getMessage().getContent();

            if (content != null) {
                System.out.println("TeamNewsMapper: Extracted content length: " + content.length() + " characters");
                System.out.println("TeamNewsMapper: Content ends with: '" +
                    content.substring(Math.max(0, content.length() - 100)) + "'");

                // Check if content looks like valid JSON
                if (!content.trim().endsWith("}")) {
                    System.out.println("TeamNewsMapper: WARNING - Content does not end with '}', might be truncated");
                    System.out.println("TeamNewsMapper: Last 200 characters: '" +
                        content.substring(Math.max(0, content.length() - 200)) + "'");
                }
            } else {
                System.out.println("TeamNewsMapper: Extracted content is null");
            }

            return content;
        }

        System.out.println("TeamNewsMapper: XaiApiResponse is null or has no choices");
        return "";
    }

    private TeamNewsData.TokenUsage mapTokenUsage(XaiApiResponse.Usage usage) {
        if (usage == null) {
            return TeamNewsData.TokenUsage.builder()
                    .promptTokens(0)
                    .completionTokens(0)
                    .totalTokens(0)
                    .build();
        }
        
        return TeamNewsData.TokenUsage.builder()
                .promptTokens(usage.getPromptTokens())
                .completionTokens(usage.getCompletionTokens())
                .totalTokens(usage.getTotalTokens())
                .build();
    }

    /**
     * Creates a mapping of team names to their possible translations/variations
     * This helps match team names in different languages
     */
    public static Map<String, List<String>> createTeamNameVariations(String homeTeam, String awayTeam) {
        Map<String, List<String>> variations = new HashMap<>();

        // Add common team name translations
        Map<String, List<String>> commonTranslations = getCommonTeamTranslations();

        // Add variations for home team
        List<String> homeVariations = List.of(homeTeam.toLowerCase());
        if (commonTranslations.containsKey(homeTeam.toLowerCase())) {
            homeVariations = commonTranslations.get(homeTeam.toLowerCase());
        }
        variations.put("home", homeVariations);

        // Add variations for away team
        List<String> awayVariations = List.of(awayTeam.toLowerCase());
        if (commonTranslations.containsKey(awayTeam.toLowerCase())) {
            awayVariations = commonTranslations.get(awayTeam.toLowerCase());
        }
        variations.put("away", awayVariations);

        return variations;
    }

    private static Map<String, List<String>> getCommonTeamTranslations() {
        Map<String, List<String>> translations = new HashMap<>();

        // Add common team translations (English -> other languages)
        translations.put("germany", List.of("germany", "германия", "alemania", "allemagne", "deutschland"));
        translations.put("portugal", List.of("portugal", "португалия", "portugal", "portugal", "portugal"));
        translations.put("spain", List.of("spain", "испания", "españa", "espagne", "spanien"));
        translations.put("france", List.of("france", "франция", "francia", "france", "frankreich"));
        translations.put("italy", List.of("italy", "италия", "italia", "italie", "italien"));
        translations.put("england", List.of("england", "англия", "inglaterra", "angleterre", "england"));
        translations.put("brazil", List.of("brazil", "бразилия", "brasil", "brésil", "brasilien"));
        translations.put("argentina", List.of("argentina", "аржентина", "argentina", "argentine", "argentinien"));

        // Add club teams
        translations.put("barcelona", List.of("barcelona", "барселона", "barcelona", "barcelone", "barcelona"));
        translations.put("real madrid", List.of("real madrid", "реал мадрид", "real madrid", "real madrid", "real madrid"));
        translations.put("manchester united", List.of("manchester united", "манчестър юнайтед", "manchester united", "manchester united", "manchester united"));
        translations.put("bayern munich", List.of("bayern munich", "байерн мюнхен", "bayern múnich", "bayern munich", "bayern münchen"));

        return translations;
    }


}
