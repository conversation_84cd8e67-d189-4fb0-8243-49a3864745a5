package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.health;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client.XaiApiClient;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.config.TeamNewsConfigurationProperties;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring.MetricsCollector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * Health indicator for Team News functionality.
 * Provides health checks for various components and dependencies.
 */
@Slf4j
@Component("teamNewsHealth")
@RequiredArgsConstructor
public class TeamNewsHealthIndicator implements HealthIndicator {
    
    private final XaiApiClient xaiApiClient;
    private final TeamNewsConfigurationProperties config;
    private final MetricsCollector metricsCollector;
    
    @Override
    public Health health() {
        try {
            Health.Builder builder = Health.up();
            
            // Check configuration
            checkConfiguration(builder);
            
            // Check metrics collector
            checkMetricsCollector(builder);
            
            // Check API connectivity (if enabled)
            if (config.getMonitoring().isEnabled()) {
                checkApiConnectivity(builder);
            }
            
            // Add system information
            addSystemInfo(builder);
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("Health check failed", e);
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", Instant.now().toString())
                    .build();
        }
    }
    
    private void checkConfiguration(Health.Builder builder) {
        try {
            // Validate essential configuration
            if (config.getApi().getModel() == null || config.getApi().getModel().isEmpty()) {
                builder.down().withDetail("config.api.model", "Missing or empty");
                return;
            }
            
            if (config.getApi().getMaxTokens() <= 0) {
                builder.down().withDetail("config.api.maxTokens", "Invalid value: " + config.getApi().getMaxTokens());
                return;
            }
            
            builder.withDetail("config.status", "OK")
                   .withDetail("config.api.model", config.getApi().getModel())
                   .withDetail("config.api.maxTokens", config.getApi().getMaxTokens())
                   .withDetail("config.monitoring.enabled", config.getMonitoring().isEnabled());
            
        } catch (Exception e) {
            builder.down().withDetail("config.error", e.getMessage());
        }
    }
    
    private void checkMetricsCollector(Health.Builder builder) {
        try {
            MetricsCollector.MetricsSummary summary = metricsCollector.getMetricsSummary();
            
            builder.withDetail("metrics.totalOperations", summary.getTotalOperations())
                   .withDetail("metrics.successRate", String.format("%.2f%%", summary.getSuccessRate() * 100))
                   .withDetail("metrics.averageResponseTime", summary.getAverageResponseTime().toMillis() + "ms")
                   .withDetail("metrics.totalTokensUsed", summary.getTotalTokensUsed());
            
            // Check if success rate is acceptable (>= 90%)
            if (summary.getTotalOperations() > 10 && summary.getSuccessRate() < 0.9) {
                builder.down().withDetail("metrics.warning", "Low success rate: " + 
                                        String.format("%.2f%%", summary.getSuccessRate() * 100));
            }
            
        } catch (Exception e) {
            builder.down().withDetail("metrics.error", e.getMessage());
        }
    }
    
    private void checkApiConnectivity(Health.Builder builder) {
        try {
            // Simple connectivity check - we'll just verify the client is available
            // In a real implementation, you might want to make a lightweight API call
            if (xaiApiClient != null) {
                builder.withDetail("api.connectivity", "OK")
                       .withDetail("api.client", "Available");
            } else {
                builder.down().withDetail("api.connectivity", "FAILED")
                             .withDetail("api.client", "Not available");
            }
            
        } catch (Exception e) {
            builder.down().withDetail("api.connectivity", "FAILED")
                         .withDetail("api.error", e.getMessage());
        }
    }
    
    private void addSystemInfo(Health.Builder builder) {
        builder.withDetail("timestamp", Instant.now().toString())
               .withDetail("component", "TeamNewsService")
               .withDetail("version", "1.0.0");
    }
}
