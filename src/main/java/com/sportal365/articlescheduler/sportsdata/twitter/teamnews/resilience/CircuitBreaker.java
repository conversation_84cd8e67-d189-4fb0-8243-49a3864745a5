package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.resilience;

import java.time.Duration;
import java.util.function.Supplier;

/**
 * Circuit breaker interface for handling failures and preventing cascading failures.
 * Implements the Circuit Breaker pattern for resilience.
 */
public interface CircuitBreaker {
    
    /**
     * Executes a supplier with circuit breaker protection.
     * 
     * @param supplier The supplier to execute
     * @param <T> The return type
     * @return The result of the supplier
     * @throws CircuitBreakerOpenException if the circuit is open
     */
    <T> T execute(Supplier<T> supplier) throws CircuitBreakerOpenException;
    
    /**
     * Executes a supplier with circuit breaker protection and fallback.
     * 
     * @param supplier The supplier to execute
     * @param fallback The fallback supplier if circuit is open or execution fails
     * @param <T> The return type
     * @return The result of the supplier or fallback
     */
    <T> T executeWithFallback(Supplier<T> supplier, Supplier<T> fallback);
    
    /**
     * Gets the current state of the circuit breaker.
     * 
     * @return The current state
     */
    CircuitBreakerState getState();
    
    /**
     * Gets the current metrics of the circuit breaker.
     * 
     * @return The current metrics
     */
    CircuitBreakerMetrics getMetrics();
    
    /**
     * Manually opens the circuit breaker.
     */
    void open();
    
    /**
     * Manually closes the circuit breaker.
     */
    void close();
    
    /**
     * Resets the circuit breaker to its initial state.
     */
    void reset();
    
    /**
     * Circuit breaker states.
     */
    enum CircuitBreakerState {
        CLOSED,    // Normal operation
        OPEN,      // Circuit is open, calls are failing fast
        HALF_OPEN  // Testing if the service has recovered
    }
    
    /**
     * Circuit breaker configuration.
     */
    interface Configuration {
        /**
         * Gets the failure threshold percentage (0.0 to 1.0).
         * 
         * @return The failure threshold
         */
        double getFailureThreshold();
        
        /**
         * Gets the minimum number of calls before evaluating failure rate.
         * 
         * @return The minimum call count
         */
        int getMinimumCallCount();
        
        /**
         * Gets the wait duration in open state before transitioning to half-open.
         * 
         * @return The wait duration
         */
        Duration getWaitDurationInOpenState();
        
        /**
         * Gets the number of permitted calls in half-open state.
         * 
         * @return The permitted call count
         */
        int getPermittedCallsInHalfOpenState();
        
        /**
         * Gets the sliding window size for failure rate calculation.
         * 
         * @return The sliding window size
         */
        int getSlidingWindowSize();
    }
    
    /**
     * Circuit breaker metrics.
     */
    interface CircuitBreakerMetrics {
        /**
         * Gets the total number of calls.
         * 
         * @return The total call count
         */
        long getTotalCalls();
        
        /**
         * Gets the number of successful calls.
         * 
         * @return The successful call count
         */
        long getSuccessfulCalls();
        
        /**
         * Gets the number of failed calls.
         * 
         * @return The failed call count
         */
        long getFailedCalls();
        
        /**
         * Gets the current failure rate (0.0 to 1.0).
         * 
         * @return The failure rate
         */
        double getFailureRate();
        
        /**
         * Gets the number of calls not permitted due to open circuit.
         * 
         * @return The not permitted call count
         */
        long getNotPermittedCalls();
    }
    
    /**
     * Exception thrown when circuit breaker is open.
     */
    class CircuitBreakerOpenException extends RuntimeException {
        public CircuitBreakerOpenException(String message) {
            super(message);
        }
        
        public CircuitBreakerOpenException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
