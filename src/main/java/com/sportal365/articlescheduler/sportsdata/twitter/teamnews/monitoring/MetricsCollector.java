package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring;

import java.time.Duration;
import java.util.Map;

/**
 * Interface for collecting metrics from team news operations.
 * Provides monitoring and observability for the team news system.
 */
public interface MetricsCollector {
    
    /**
     * Records the start of an operation.
     * 
     * @param operationName The name of the operation
     * @param context Additional context information
     * @return Operation ID for tracking
     */
    String startOperation(String operationName, Map<String, Object> context);
    
    /**
     * Records the completion of an operation.
     * 
     * @param operationId The operation ID from startOperation
     * @param success Whether the operation was successful
     * @param result Additional result information
     */
    void endOperation(String operationId, boolean success, Map<String, Object> result);
    
    /**
     * Records a counter metric.
     * 
     * @param metricName The name of the metric
     * @param value The value to add to the counter
     * @param tags Additional tags for the metric
     */
    void incrementCounter(String metricName, long value, Map<String, String> tags);
    
    /**
     * Records a gauge metric.
     * 
     * @param metricName The name of the metric
     * @param value The current value
     * @param tags Additional tags for the metric
     */
    void recordGauge(String metricName, double value, Map<String, String> tags);
    
    /**
     * Records a timing metric.
     * 
     * @param metricName The name of the metric
     * @param duration The duration to record
     * @param tags Additional tags for the metric
     */
    void recordTiming(String metricName, Duration duration, Map<String, String> tags);
    
    /**
     * Records token usage metrics.
     * 
     * @param usage The token usage information
     */
    void recordTokenUsage(TokenUsage usage);
    
    /**
     * Records API call metrics.
     * 
     * @param apiCall The API call information
     */
    void recordApiCall(ApiCallMetrics apiCall);
    
    /**
     * Gets current metrics summary.
     * 
     * @return Current metrics summary
     */
    MetricsSummary getMetricsSummary();
    
    /**
     * Token usage information.
     */
    interface TokenUsage {
        String getOperationType();
        int getPromptTokens();
        int getCompletionTokens();
        int getTotalTokens();
        String getModel();
        long getTimestamp();
    }
    
    /**
     * API call metrics.
     */
    interface ApiCallMetrics {
        String getApiName();
        String getEndpoint();
        int getStatusCode();
        Duration getResponseTime();
        boolean isSuccess();
        String getErrorMessage();
        long getTimestamp();
    }
    
    /**
     * Summary of collected metrics.
     */
    interface MetricsSummary {
        long getTotalOperations();
        long getSuccessfulOperations();
        long getFailedOperations();
        double getSuccessRate();
        Duration getAverageResponseTime();
        long getTotalTokensUsed();
        Map<String, Long> getOperationCounts();
        Map<String, Double> getAverageTimings();
    }
}
