package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring;

import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.config.TeamNewsConfigurationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Implementation of MetricsCollector for team news operations.
 * Collects and tracks various metrics for monitoring and observability.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsMetricsCollector implements MetricsCollector {
    
    private final TeamNewsConfigurationProperties config;
    
    // Operation tracking
    private final Map<String, OperationContext> activeOperations = new ConcurrentHashMap<>();
    private final AtomicLong operationIdCounter = new AtomicLong(0);
    
    // Metrics storage
    private final LongAdder totalOperations = new LongAdder();
    private final LongAdder successfulOperations = new LongAdder();
    private final LongAdder failedOperations = new LongAdder();
    private final LongAdder totalTokensUsed = new LongAdder();
    
    private final Map<String, LongAdder> operationCounts = new ConcurrentHashMap<>();
    private final Map<String, LongAdder> timingCounts = new ConcurrentHashMap<>();
    private final Map<String, LongAdder> timingSums = new ConcurrentHashMap<>();
    
    @Override
    public String startOperation(String operationName, Map<String, Object> context) {
        if (!config.getMonitoring().isEnabled()) {
            return null;
        }
        
        String operationId = "op_" + operationIdCounter.incrementAndGet();
        OperationContext opContext = new OperationContext(operationName, Instant.now(), context);
        activeOperations.put(operationId, opContext);
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Started operation: {} with ID: {}", operationName, operationId);
        }
        
        return operationId;
    }
    
    @Override
    public void endOperation(String operationId, boolean success, Map<String, Object> result) {
        if (!config.getMonitoring().isEnabled() || operationId == null) {
            return;
        }
        
        OperationContext context = activeOperations.remove(operationId);
        if (context == null) {
            log.warn("Operation context not found for ID: {}", operationId);
            return;
        }
        
        Duration duration = Duration.between(context.getStartTime(), Instant.now());
        
        totalOperations.increment();
        if (success) {
            successfulOperations.increment();
        } else {
            failedOperations.increment();
        }
        
        operationCounts.computeIfAbsent(context.getOperationName(), k -> new LongAdder()).increment();
        
        if (config.getMonitoring().isTrackProcessingTimes()) {
            String timingKey = context.getOperationName() + "_timing";
            timingCounts.computeIfAbsent(timingKey, k -> new LongAdder()).increment();
            timingSums.computeIfAbsent(timingKey, k -> new LongAdder()).add(duration.toMillis());
        }
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Completed operation: {} (ID: {}) in {}ms, success: {}", 
                     context.getOperationName(), operationId, duration.toMillis(), success);
        }
    }
    
    @Override
    public void incrementCounter(String metricName, long value, Map<String, String> tags) {
        if (!config.getMonitoring().isEnabled()) {
            return;
        }
        
        String key = buildMetricKey(metricName, tags);
        operationCounts.computeIfAbsent(key, k -> new LongAdder()).add(value);
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Incremented counter: {} by {}", key, value);
        }
    }
    
    @Override
    public void recordGauge(String metricName, double value, Map<String, String> tags) {
        if (!config.getMonitoring().isEnabled()) {
            return;
        }
        
        // For simplicity, we'll log gauge values
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Recorded gauge: {} = {}", buildMetricKey(metricName, tags), value);
        }
    }
    
    @Override
    public void recordTiming(String metricName, Duration duration, Map<String, String> tags) {
        if (!config.getMonitoring().isEnabled()) {
            return;
        }
        
        String key = buildMetricKey(metricName, tags);
        timingCounts.computeIfAbsent(key, k -> new LongAdder()).increment();
        timingSums.computeIfAbsent(key, k -> new LongAdder()).add(duration.toMillis());
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Recorded timing: {} = {}ms", key, duration.toMillis());
        }
    }
    
    @Override
    public void recordTokenUsage(TokenUsage usage) {
        if (!config.getMonitoring().isEnabled() || !config.getMonitoring().isTrackTokenUsage()) {
            return;
        }
        
        totalTokensUsed.add(usage.getTotalTokens());
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Recorded token usage: {} tokens for {} operation", 
                     usage.getTotalTokens(), usage.getOperationType());
        }
    }
    
    @Override
    public void recordApiCall(ApiCallMetrics apiCall) {
        if (!config.getMonitoring().isEnabled()) {
            return;
        }
        
        String key = "api_call_" + apiCall.getApiName();
        operationCounts.computeIfAbsent(key, k -> new LongAdder()).increment();
        
        if (apiCall.isSuccess()) {
            operationCounts.computeIfAbsent(key + "_success", k -> new LongAdder()).increment();
        } else {
            operationCounts.computeIfAbsent(key + "_failure", k -> new LongAdder()).increment();
        }
        
        recordTiming(key + "_response_time", apiCall.getResponseTime(), Map.of());
        
        if (config.getMonitoring().isLogMetrics()) {
            log.debug("Recorded API call: {} - {} in {}ms", 
                     apiCall.getApiName(), apiCall.isSuccess() ? "SUCCESS" : "FAILURE", 
                     apiCall.getResponseTime().toMillis());
        }
    }
    
    @Override
    public MetricsSummary getMetricsSummary() {
        return new TeamNewsMetricsSummary();
    }
    
    private String buildMetricKey(String metricName, Map<String, String> tags) {
        if (tags == null || tags.isEmpty()) {
            return metricName;
        }
        
        StringBuilder key = new StringBuilder(metricName);
        tags.forEach((k, v) -> key.append("_").append(k).append("_").append(v));
        return key.toString();
    }
    
    private static class OperationContext {
        private final String operationName;
        private final Instant startTime;
        private final Map<String, Object> context;
        
        public OperationContext(String operationName, Instant startTime, Map<String, Object> context) {
            this.operationName = operationName;
            this.startTime = startTime;
            this.context = context;
        }
        
        public String getOperationName() { return operationName; }
        public Instant getStartTime() { return startTime; }
        public Map<String, Object> getContext() { return context; }
    }
    
    private class TeamNewsMetricsSummary implements MetricsSummary {
        @Override
        public long getTotalOperations() {
            return totalOperations.sum();
        }
        
        @Override
        public long getSuccessfulOperations() {
            return successfulOperations.sum();
        }
        
        @Override
        public long getFailedOperations() {
            return failedOperations.sum();
        }
        
        @Override
        public double getSuccessRate() {
            long total = getTotalOperations();
            return total > 0 ? (double) getSuccessfulOperations() / total : 0.0;
        }
        
        @Override
        public Duration getAverageResponseTime() {
            // Calculate average from all timing metrics
            long totalTime = timingSums.values().stream().mapToLong(LongAdder::sum).sum();
            long totalCount = timingCounts.values().stream().mapToLong(LongAdder::sum).sum();
            return totalCount > 0 ? Duration.ofMillis(totalTime / totalCount) : Duration.ZERO;
        }
        
        @Override
        public long getTotalTokensUsed() {
            return totalTokensUsed.sum();
        }
        
        @Override
        public Map<String, Long> getOperationCounts() {
            Map<String, Long> counts = new ConcurrentHashMap<>();
            operationCounts.forEach((k, v) -> counts.put(k, v.sum()));
            return counts;
        }
        
        @Override
        public Map<String, Double> getAverageTimings() {
            Map<String, Double> averages = new ConcurrentHashMap<>();
            timingCounts.forEach((k, count) -> {
                LongAdder sum = timingSums.get(k);
                if (sum != null && count.sum() > 0) {
                    averages.put(k, (double) sum.sum() / count.sum());
                }
            });
            return averages;
        }
    }
}
