package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client.XaiApiClient;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper.TeamNewsResponseMapper;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamNewsService implements MatchDetailEnrichService {

    private final XaiApiClient xaiApiClient;
    private final TeamNewsMapper teamNewsMapper;
    private final TeamNewsResponseMapper responseMapper;

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        try {
            log.info("Enriching match details with team news for match: {}", matchDetails.getMatchId());

            TeamNewsData teamNewsData = getTeamNews(matchDetails, schedule).block();

            if (teamNewsData != null) {
                MatchDetails.TeamNews teamNews = responseMapper.mapToMatchDetailsTeamNews(teamNewsData);
                MatchDetails.Quotes quotes = responseMapper.mapToMatchDetailsQuotes(teamNewsData);

                return matchDetails.toBuilder()
                        .teamNews(teamNews)
                        .quotes(quotes)
                        .build();
            } else {
                log.warn("No team news data received for match: {}", matchDetails.getMatchId());
                return matchDetails;
            }

        } catch (Exception e) {
            log.error("Error enriching match details with team news for match: {}. Error: {}",
                     matchDetails.getMatchId(), e.getMessage(), e);
            // Return original match details on error to not break the pipeline
            return matchDetails;
        }
    }

    private Mono<TeamNewsData> getTeamNews(MatchDetails matchDetails, Schedule schedule) {
        log.info("Fetching team news for match: {} vs {}",
                  matchDetails.getHomeTeam(),
                  matchDetails.getAwayTeam());

        XaiApiRequest request = teamNewsMapper.mapToXaiRequest(matchDetails, schedule);

        return xaiApiClient.getTeamNews(request)
                .map(response -> teamNewsMapper.mapToTeamNewsData(matchDetails, response))
                .doOnSuccess(teamNews -> log.debug("Successfully retrieved team news for match: {}", matchDetails.getMatchId()))
                .doOnError(error -> log.error("Error retrieving team news for match: {}. Error: {}",
                                             matchDetails.getMatchId(), error.getMessage(), error));
    }
}
