package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.client.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
@Configuration
public class XaiApiClientConfiguration {

    @Value("${xai.api.base.url:https://api.x.ai}")
    private String xaiApiBaseUrl;

    @Value("${xai.api.key:}")
    private String xaiApiKey;

    @Bean
    public WebClient xaiApiWebClient() {
        if (xaiApiKey == null || xaiApiKey.trim().isEmpty()) {
            log.warn("X.AI API key is not configured. Team news functionality will be disabled.");
        }

        return WebClient.builder()
                .baseUrl(xaiApiBaseUrl)
                .defaultHeaders(headers -> {
                    headers.set("Content-Type", "application/json");
                    if (xaiApiKey != null && !xaiApiKey.trim().isEmpty()) {
                        headers.set("Authorization", "Bearer " + xaiApiKey);
                    }
                })
                .build();
    }
}
