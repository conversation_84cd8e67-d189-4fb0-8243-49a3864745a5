package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.resilience;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

/**
 * Implementation of CircuitBreaker for Team News operations.
 * Provides resilience against cascading failures.
 */
@Slf4j
@Component
public class TeamNewsCircuitBreaker implements CircuitBreaker {
    
    private final Configuration configuration;
    private final AtomicReference<CircuitBreakerState> state;
    private final AtomicLong totalCalls;
    private final AtomicLong successfulCalls;
    private final AtomicLong failedCalls;
    private final AtomicLong notPermittedCalls;
    private final AtomicReference<Instant> lastFailureTime;
    private final AtomicLong halfOpenCalls;
    
    public TeamNewsCircuitBreaker() {
        this.configuration = new DefaultConfiguration();
        this.state = new AtomicReference<>(CircuitBreakerState.CLOSED);
        this.totalCalls = new AtomicLong(0);
        this.successfulCalls = new AtomicLong(0);
        this.failedCalls = new AtomicLong(0);
        this.notPermittedCalls = new AtomicLong(0);
        this.lastFailureTime = new AtomicReference<>(Instant.now());
        this.halfOpenCalls = new AtomicLong(0);
    }
    
    @Override
    public <T> T execute(Supplier<T> supplier) throws CircuitBreakerOpenException {
        if (!isCallPermitted()) {
            notPermittedCalls.incrementAndGet();
            throw new CircuitBreakerOpenException("Circuit breaker is OPEN");
        }
        
        totalCalls.incrementAndGet();
        
        try {
            T result = supplier.get();
            onSuccess();
            return result;
        } catch (Exception e) {
            onFailure();
            throw e;
        }
    }
    
    @Override
    public <T> T executeWithFallback(Supplier<T> supplier, Supplier<T> fallback) {
        try {
            return execute(supplier);
        } catch (Exception e) {
            log.warn("Circuit breaker execution failed, using fallback: {}", e.getMessage());
            return fallback.get();
        }
    }
    
    @Override
    public CircuitBreakerState getState() {
        return state.get();
    }
    
    @Override
    public CircuitBreakerMetrics getMetrics() {
        return new TeamNewsCircuitBreakerMetrics();
    }
    
    @Override
    public void open() {
        state.set(CircuitBreakerState.OPEN);
        lastFailureTime.set(Instant.now());
        log.warn("Circuit breaker manually opened");
    }
    
    @Override
    public void close() {
        state.set(CircuitBreakerState.CLOSED);
        halfOpenCalls.set(0);
        log.info("Circuit breaker manually closed");
    }
    
    @Override
    public void reset() {
        state.set(CircuitBreakerState.CLOSED);
        totalCalls.set(0);
        successfulCalls.set(0);
        failedCalls.set(0);
        notPermittedCalls.set(0);
        halfOpenCalls.set(0);
        lastFailureTime.set(Instant.now());
        log.info("Circuit breaker reset");
    }
    
    private boolean isCallPermitted() {
        CircuitBreakerState currentState = state.get();
        
        switch (currentState) {
            case CLOSED:
                return true;
            case OPEN:
                return tryToTransitionToHalfOpen();
            case HALF_OPEN:
                return halfOpenCalls.get() < configuration.getPermittedCallsInHalfOpenState();
            default:
                return false;
        }
    }
    
    private boolean tryToTransitionToHalfOpen() {
        Instant now = Instant.now();
        Instant lastFailure = lastFailureTime.get();
        
        if (Duration.between(lastFailure, now).compareTo(configuration.getWaitDurationInOpenState()) >= 0) {
            if (state.compareAndSet(CircuitBreakerState.OPEN, CircuitBreakerState.HALF_OPEN)) {
                halfOpenCalls.set(0);
                log.info("Circuit breaker transitioned from OPEN to HALF_OPEN");
                return true;
            }
        }
        
        return false;
    }
    
    private void onSuccess() {
        successfulCalls.incrementAndGet();
        
        if (state.get() == CircuitBreakerState.HALF_OPEN) {
            long currentHalfOpenCalls = halfOpenCalls.incrementAndGet();
            if (currentHalfOpenCalls >= configuration.getPermittedCallsInHalfOpenState()) {
                if (state.compareAndSet(CircuitBreakerState.HALF_OPEN, CircuitBreakerState.CLOSED)) {
                    log.info("Circuit breaker transitioned from HALF_OPEN to CLOSED");
                }
            }
        }
    }
    
    private void onFailure() {
        failedCalls.incrementAndGet();
        lastFailureTime.set(Instant.now());
        
        if (state.get() == CircuitBreakerState.HALF_OPEN) {
            if (state.compareAndSet(CircuitBreakerState.HALF_OPEN, CircuitBreakerState.OPEN)) {
                log.warn("Circuit breaker transitioned from HALF_OPEN to OPEN due to failure");
            }
        } else if (state.get() == CircuitBreakerState.CLOSED) {
            if (shouldOpenCircuit()) {
                if (state.compareAndSet(CircuitBreakerState.CLOSED, CircuitBreakerState.OPEN)) {
                    log.warn("Circuit breaker transitioned from CLOSED to OPEN due to failure rate");
                }
            }
        }
    }
    
    private boolean shouldOpenCircuit() {
        long total = totalCalls.get();
        long failed = failedCalls.get();
        
        if (total < configuration.getMinimumCallCount()) {
            return false;
        }
        
        double failureRate = (double) failed / total;
        return failureRate >= configuration.getFailureThreshold();
    }
    
    private static class DefaultConfiguration implements Configuration {
        @Override
        public double getFailureThreshold() {
            return 0.5; // 50% failure rate
        }
        
        @Override
        public int getMinimumCallCount() {
            return 10;
        }
        
        @Override
        public Duration getWaitDurationInOpenState() {
            return Duration.ofSeconds(30);
        }
        
        @Override
        public int getPermittedCallsInHalfOpenState() {
            return 3;
        }
        
        @Override
        public int getSlidingWindowSize() {
            return 100;
        }
    }
    
    private class TeamNewsCircuitBreakerMetrics implements CircuitBreakerMetrics {
        @Override
        public long getTotalCalls() {
            return totalCalls.get();
        }
        
        @Override
        public long getSuccessfulCalls() {
            return successfulCalls.get();
        }
        
        @Override
        public long getFailedCalls() {
            return failedCalls.get();
        }
        
        @Override
        public double getFailureRate() {
            long total = getTotalCalls();
            return total > 0 ? (double) getFailedCalls() / total : 0.0;
        }
        
        @Override
        public long getNotPermittedCalls() {
            return notPermittedCalls.get();
        }
    }
}
