package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Configuration properties for Team News functionality.
 * Externalizes all constants and configuration values for better maintainability.
 */
@Data
@ConfigurationProperties(prefix = "team-news")
public class TeamNewsConfigurationProperties {
    
    /**
     * API configuration settings.
     */
    private Api api = new Api();
    
    /**
     * Content processing configuration.
     */
    private Processing processing = new Processing();
    
    /**
     * Strategy selection configuration.
     */
    private Strategy strategy = new Strategy();
    
    /**
     * Monitoring and metrics configuration.
     */
    private Monitoring monitoring = new Monitoring();
    
    @Data
    public static class Api {
        /**
         * XAI API model to use.
         */
        private String model = "grok-3-latest";
        
        /**
         * Default temperature for API requests.
         */
        private int temperature = 0;
        
        /**
         * Maximum tokens for API responses.
         */
        private int maxTokens = 1500;
        
        /**
         * Maximum search results to retrieve.
         */
        private int maxSearchResults = 20;
        
        /**
         * Whether to stream responses.
         */
        private boolean stream = false;
        
        /**
         * Whether to return citations.
         */
        private boolean returnCitations = true;
        
        /**
         * Search mode for live search.
         */
        private String searchMode = "on";
        
        /**
         * Default X handles to search.
         */
        private List<String> defaultXHandles = List.of("UEFA", "FIFAcom");
        
        /**
         * Source types for search.
         */
        private SourceTypes sourceTypes = new SourceTypes();
        
        /**
         * Search date range in days.
         */
        private int searchDateRangeDays = 30;
    }
    
    @Data
    public static class SourceTypes {
        private String x = "x";
        private String web = "web";
        private String news = "news";
    }
    
    @Data
    public static class Processing {
        /**
         * Content processing pipeline configuration.
         */
        private Pipeline pipeline = new Pipeline();
        
        /**
         * Content cleaning configuration.
         */
        private Cleaning cleaning = new Cleaning();
    }
    
    @Data
    public static class Pipeline {
        /**
         * Whether to enable the processing pipeline.
         */
        private boolean enabled = true;
        
        /**
         * Order of processors in the pipeline.
         */
        private List<String> processorOrder = List.of(
            "MarkdownHeaderProcessor",
            "SpecialSymbolProcessor", 
            "TeamSectionExtractor",
            "WhitespaceProcessor"
        );
        
        /**
         * Whether to log processing steps.
         */
        private boolean logSteps = true;
    }
    
    @Data
    public static class Cleaning {
        /**
         * Whether to remove markdown headers.
         */
        private boolean removeHeaders = true;
        
        /**
         * Whether to remove special symbols.
         */
        private boolean removeSpecialSymbols = true;
        
        /**
         * Whether to normalize whitespace.
         */
        private boolean normalizeWhitespace = true;
        
        /**
         * Whether to extract team sections.
         */
        private boolean extractTeamSections = true;
    }
    
    @Data
    public static class Strategy {
        /**
         * Default parsing strategy to use.
         */
        private String defaultParsingStrategy = "markdown";
        
        /**
         * Default cleaning strategy to use.
         */
        private String defaultCleaningStrategy = "markdown";
        
        /**
         * Whether to enable automatic strategy selection.
         */
        private boolean autoSelection = true;
        
        /**
         * Strategy selection criteria.
         */
        private Selection selection = new Selection();
    }
    
    @Data
    public static class Selection {
        /**
         * Content type detection patterns.
         */
        private ContentTypes contentTypes = new ContentTypes();
    }
    
    @Data
    public static class ContentTypes {
        /**
         * Patterns to detect markdown content.
         */
        private List<String> markdownPatterns = List.of("##", "###", "*", "_", "`");
        
        /**
         * Patterns to detect HTML content.
         */
        private List<String> htmlPatterns = List.of("<", ">", "&lt;", "&gt;");
        
        /**
         * Patterns to detect JSON content.
         */
        private List<String> jsonPatterns = List.of("{", "}", "[", "]", "\":");
    }
    
    @Data
    public static class Monitoring {
        /**
         * Whether to enable metrics collection.
         */
        private boolean enabled = true;
        
        /**
         * Whether to log performance metrics.
         */
        private boolean logMetrics = true;
        
        /**
         * Whether to track token usage.
         */
        private boolean trackTokenUsage = true;
        
        /**
         * Whether to track processing times.
         */
        private boolean trackProcessingTimes = true;
        
        /**
         * Metrics collection interval in seconds.
         */
        private int metricsIntervalSeconds = 60;
    }
}
