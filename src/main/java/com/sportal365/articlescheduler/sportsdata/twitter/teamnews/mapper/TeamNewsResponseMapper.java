package com.sportal365.articlescheduler.sportsdata.twitter.teamnews.mapper;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.model.TeamNewsData;
import com.sportal365.articlescheduler.sportsdata.twitter.teamnews.processor.JsonContentConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TeamNewsResponseMapper {

    private final ResponseToDomainMapper responseToDomainMapper;
    private final JsonContentConverter jsonContentConverter;

    public MatchDetails.TeamNews mapToMatchDetailsTeamNews(TeamNewsData teamNewsData) {
        try {
            String content = teamNewsData.getMarkdownContent();
            if (content == null || content.isEmpty()) {
                return null;
            }

            // Extract the first ## section (Team News)
            String teamNewsContent = extractSectionContent(content, 0);

            if (teamNewsContent == null || teamNewsContent.trim().isEmpty()) {
                return null;
            }

            // Clean the content by removing special symbols
            String cleanedContent = cleanAllContent(teamNewsContent.trim());

            return MatchDetails.TeamNews.builder()
                    .teamNews(cleanedContent)
                    .build();

        } catch (Exception e) {
            log.error("Error mapping team news data: {}", e.getMessage(), e);
            return null;
        }
    }

    public MatchDetails.Quotes mapToMatchDetailsQuotes(TeamNewsData teamNewsData) {
        try {
            String content = teamNewsData.getMarkdownContent();
            if (content == null || content.isEmpty()) {
                return null;
            }

            // Extract the second ## section (Manager Quotes)
            String managersQuotesContent = extractSectionContent(content, 1);

            // Extract the third ## section (Player Quotes)
            String playersQuotesContent = extractSectionContent(content, 2);

            // Return null if both quotes are empty
            if ((managersQuotesContent == null || managersQuotesContent.trim().isEmpty()) &&
                    (playersQuotesContent == null || playersQuotesContent.trim().isEmpty())) {
                return null;
            }

            // Clean the quotes content by removing special symbols
            String cleanedManagersQuotes = managersQuotesContent != null ? cleanAllContent(managersQuotesContent.trim()) : null;
            String cleanedPlayersQuotes = playersQuotesContent != null ? cleanAllContent(playersQuotesContent.trim()) : null;

            return MatchDetails.Quotes.builder()
                    .managersQuotes(cleanedManagersQuotes)
                    .playersQuotes(cleanedPlayersQuotes)
                    .build();

        } catch (Exception e) {
            log.error("Error mapping quotes data: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extracts content from a specific ## section by index (0-based)
     * @param content The markdown content
     * @param sectionIndex The index of the ## section to extract (0 = first ##, 1 = second ##, etc.)
     * @return The content of the specified section, or null if not found
     */
    private String extractSectionContent(String content, int sectionIndex) {
        if (content == null || content.isEmpty()) {
            return null;
        }

        // Split content by ## headers
        String[] sections = content.split("(?m)^## ");

        // The first element might be empty or contain content before the first ##
        // Adjust index to account for this
        int actualIndex = sectionIndex + 1; // +1 because split creates empty first element

        if (actualIndex >= sections.length) {
            return null;
        }

        String sectionContent = sections[actualIndex];

        // Remove the header line (everything up to the first newline)
        int firstNewlineIndex = sectionContent.indexOf('\n');
        if (firstNewlineIndex != -1) {
            sectionContent = sectionContent.substring(firstNewlineIndex + 1);
        }

        return sectionContent.trim();
    }

    /**
     * Converts MatchDetails.TeamNews content to clean JSON format by removing all special symbols
     * @param teamNews The TeamNews object containing markdown content
     * @return JSON string with clean content structure
     */
    public String convertTeamNewsToJson(MatchDetails.TeamNews teamNews) {
        try {
            if (teamNews == null || teamNews.getTeamNews() == null || teamNews.getTeamNews().isEmpty()) {
                return "{}";
            }

            String content = teamNews.getTeamNews();
            ObjectNode jsonNode = objectMapper.createObjectNode();

            // Parse team sections from the content
            String[] teamSections = parseTeamSections(content);

            if (teamSections.length >= 1 && !teamSections[0].trim().isEmpty()) {
                jsonNode.put("homeTeam", cleanContent(teamSections[0]));
            }

            if (teamSections.length >= 2 && !teamSections[1].trim().isEmpty()) {
                jsonNode.put("awayTeam", cleanContent(teamSections[1]));
            }

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode);

        } catch (Exception e) {
            log.error("Error converting team news to JSON: {}", e.getMessage(), e);
            return "{}";
        }
    }

    /**
     * Parses team sections from markdown content
     * @param content The markdown content
     * @return Array of team sections
     */
    private String[] parseTeamSections(String content) {
        if (content == null || content.isEmpty()) {
            return new String[0];
        }

        // Split by ### headers (team names)
        String[] sections = content.split("(?m)^### ");

        // Remove empty first section if it exists
        if (sections.length > 0 && sections[0].trim().isEmpty()) {
            String[] newSections = new String[sections.length - 1];
            System.arraycopy(sections, 1, newSections, 0, sections.length - 1);
            return newSections;
        }

        return sections.length > 1 ? java.util.Arrays.copyOfRange(sections, 1, sections.length) : sections;
    }

    /**
     * Cleans content by removing special symbols and formatting
     * @param content The content to clean
     * @return Clean content without special symbols
     */
    private String cleanContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Remove special symbols
        String cleaned = SPECIAL_SYMBOLS_PATTERN.matcher(content).replaceAll("");

        // Clean up multiple spaces and newlines
        cleaned = cleaned.replaceAll("\\s+", " ");

        // Remove leading/trailing whitespace
        cleaned = cleaned.trim();

        // Remove team name from the beginning if it exists (first line)
        String[] lines = cleaned.split("\\n", 2);
        if (lines.length > 1) {
            cleaned = lines[1].trim();
        }

        return cleaned;
    }

    /**
     * Cleans all content by removing special symbols and formatting from the entire text
     * @param content The content to clean
     * @return Clean content without special symbols
     */
    private String cleanAllContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Remove special symbols: **, ###, =, ---, -, *, _, `, ~, etc.
        String cleaned = content;

        // Remove markdown headers (### and ##)
        cleaned = cleaned.replaceAll("(?m)^#{1,6}\\s*", "");

        // Remove bold/italic markers
        cleaned = cleaned.replaceAll("\\*{1,2}([^*]+)\\*{1,2}", "$1");

        // Remove bullet points and dashes
        cleaned = cleaned.replaceAll("(?m)^\\s*[-*+]\\s*", "");

        // Remove horizontal rules
        cleaned = cleaned.replaceAll("(?m)^\\s*[-=]{3,}\\s*$", "");

        // Remove underscores used for emphasis
        cleaned = cleaned.replaceAll("_{1,2}([^_]+)_{1,2}", "$1");

        // Remove backticks
        cleaned = cleaned.replaceAll("`([^`]+)`", "$1");

        // Clean up multiple spaces and normalize whitespace
        cleaned = cleaned.replaceAll("\\s+", " ");

        // Remove excessive newlines
        cleaned = cleaned.replaceAll("\\n\\s*\\n", "\n");

        // Remove leading/trailing whitespace
        cleaned = cleaned.trim();

        return cleaned;
    }
}