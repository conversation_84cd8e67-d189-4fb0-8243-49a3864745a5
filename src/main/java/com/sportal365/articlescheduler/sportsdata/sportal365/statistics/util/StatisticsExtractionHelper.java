package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util;


import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiAggregateResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.TeamInfoWrapper;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants.*;


public class StatisticsExtractionHelper {

    /**
     * Extracts the IDs of the last meetings between the 2 teams (as of now collected from the Form Guide API)
     * @param matchDetails The current pipeline-produced matchDetails
     * @return List of event IDs
     */
    public static List<String> extractLastMeetingIds(MatchDetails matchDetails) {
        if (matchDetails.getLastMeetings() == null || matchDetails.getLastMeetings().isEmpty()) {

            return Collections.emptyList();
        }
        return matchDetails.getLastMeetings().stream()
                .map(MatchDetails.LastMeeting::getId)
                .toList();
    }

    /**
     * Creates a wrapper for a team name + team players from matchDetails
     * @param participantId The ID of the target team
     * @param matchDetails The current pipeline-produced matchDetails
     * @return The wrapper for a target team
     */
    public static TeamInfoWrapper determineTeamInfo(String participantId, MatchDetails matchDetails) {
        boolean isHomeTeam = participantId.equals(matchDetails.getHomeTeamId());

        String teamName = isHomeTeam ? matchDetails.getHomeTeam() : matchDetails.getAwayTeam();

        List<StatisticsPlayer> statisticsPlayers = getStatisticsPlayers(isHomeTeam, matchDetails);

        return new TeamInfoWrapper(teamName, statisticsPlayers);
    }

    /**
     * Extracts a list of {@link com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util.StatisticsPlayer}
     * objects, constructed from team lineups if available, or team squads if not
     * @param isHomeTeam Whether the team is home or away
     * @param matchDetails The current pipeline-produced matchDetails
     * @return List of {@link com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util.StatisticsPlayer}
     */
    private static List<StatisticsPlayer> getStatisticsPlayers(boolean isHomeTeam, MatchDetails matchDetails) {
        // Try to get players from lineup first
        List<MatchDetails.Lineup.TeamDetails.Player> lineupPlayers = getPlayersFromLineup(isHomeTeam, matchDetails);

        if (!lineupPlayers.isEmpty()) {
            // Use lineup players if available, and clear the squad players to keep MatchDetails data smaller
            matchDetails.setSquad(null);

            return lineupPlayers.stream()
                    .map(StatisticsPlayerFactory::createFromLineupPlayer)
                    .toList();
        } else {
            // Fall back to squad players if lineup is empty
            List<MatchDetails.Squad.Player> squadPlayers = isHomeTeam
                    ? matchDetails.getSquad().getHomeTeam().getPlayers()
                    : matchDetails.getSquad().getAwayTeam().getPlayers();

            return squadPlayers.stream()
                    .map(StatisticsPlayerFactory::createFromSquadPlayer)
                    .toList();
        }
    }

    /**
     * Extracts the player with the highest average rating from a team, if that player
     * a) is active
     * b) has at least 3 game starts for the examined season
     * @param statistics /statistics/aggregate endpoint response
     * @param statisticsPlayers Players from team lineups or squads
     * @return The highest rated player DTO (if found)
     */
    public static ParticipantDto findHighestRatedPlayer(
            StatisticsApiAggregateResponse statistics,
            List<StatisticsPlayer> statisticsPlayers) {

        return statistics.getData().stream()
                .filter(participant -> isActivePlayer(participant, statisticsPlayers))
                .filter(StatisticsExtractionHelper::hasMinimumStarts)
                .max(Comparator.comparing(StatisticsExtractionHelper::getPlayerAverageRating))
                .orElse(null);
    }

    /**
     * Provides a safe way to retrieve player lineups from MatchDetails
     * @param isHomeTeam Whether the team is home or away
     * @param matchDetails The current pipeline-produced matchDetails
     * @return A list of lineup players if available, empty one if not
     */
    private static List<MatchDetails.Lineup.TeamDetails.Player> getPlayersFromLineup(boolean isHomeTeam,
                                                                                     MatchDetails matchDetails) {
        MatchDetails.Lineup lineup = matchDetails.getLineup();
        if (lineup == null) {
            return Collections.emptyList();
        }

        if (isHomeTeam && lineup.getHomeTeam() != null) {
            return matchDetails.getLineup().getHomeTeam().getPlayers();
        } else if (!isHomeTeam && lineup.getAwayTeam() != null) {
            return matchDetails.getLineup().getAwayTeam().getPlayers();
        }

        return Collections.emptyList();
    }

    /**
     * Determines if a player should be considered as eligible for being a "star player". A player is active if
     * a) they are neither injured nor suspended
     * b) they play in an offensive position - either a midfielder or a forward
     * @param participant Player DTO
     * @param statisticsPlayers The team players as extracted from MatchDetails lineup or squad
     * @return True if the player is eligible, false otherwise
     */
    private static boolean isActivePlayer(
            ParticipantDto participant,
            List<StatisticsPlayer> statisticsPlayers) {

        return ENTITY_TYPE_PLAYER.equals(participant.getEntityType()) &&
                statisticsPlayers.stream().anyMatch(statisticsPlayer ->
                        statisticsPlayer.getName().equals(participant.getName()) &&
                                isEligiblePlayerStatus(statisticsPlayer) &&
                                isOffensivePosition(statisticsPlayer) &&
                                statisticsPlayer.isActive());
    }

    /**
     * Returns whether a player can take part of an upcoming game, i.e. they are neither injured nor suspended
     * @param player The player model as extracted from MatchDetails
     * @return True if the player is neither injured nor suspended, false otherwise
     */
    private static boolean isEligiblePlayerStatus(StatisticsPlayer player) {
        return !LINEUP_TYPE_INJURED.equals(player.getLineupStatus()) &&
                !LINEUP_TYPE_SUSPENDED.equals(player.getLineupStatus());
    }

    /**
     * Returns whether a player can be considered a "star player" based on their position. In the beginning at least,
     * we would only consider offensive positions, since defenders and goalkeepers do not lead to interesting
     * paragraphs based solely on the statistics
     * @param player The player model as extracted from MatchDetails
     * @return True if the player is either a midfielder or a forward, false otherwise
     */
    private static boolean isOffensivePosition(StatisticsPlayer player) {
        String position = player.getPosition();
        return position.equalsIgnoreCase(POSITION_MIDFIELDER) ||
                position.equalsIgnoreCase(POSITION_FORWARD);
    }

    /**
     * Determines whether a player can be considered a "start player", based on the minimum number of games
     * they've started in
     * @param participant The player DTO
     * @return True if a player has at least {@link com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants#MINIMUM_STARTED_EVENTS}
     * number of starts, false otherwise
     */
    private static boolean hasMinimumStarts(ParticipantDto participant) {
        return getStatisticValue(participant, STATISTIC_STARTED_ID)
                .map(value -> {
                    try {
                        return Integer.parseInt(value) >= MINIMUM_STARTED_EVENTS;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .orElse(false);
    }

    /**
     * Extracts the value of the "Rating Avg" statistic of a player
     * @param participant The player DTO
     * @return The "Rating Avg" statistic value of a player, 0 if not found
     */
    private static double getPlayerAverageRating(ParticipantDto participant) {
        return getStatisticValue(participant, STATISTIC_RATING_AVG_ID)
                .map(value -> {
                    try {
                        return Double.parseDouble(value);
                    } catch (NumberFormatException e) {
                        return 0.0;
                    }
                })
                .orElse(0.0);
    }

    /**
     * Extracts the value of a participant statistic, based on the statistic ID
     * @param participant The player DTO
     * @param statisticId The ID of the target statistic
     * @return An optional, with the statistic value if found
     */
    private static Optional<String> getStatisticValue(ParticipantDto participant, String statisticId) {
        return participant.getStatistics().stream()
                .filter(stat -> statisticId.equals(stat.getId()))
                .findFirst()
                .map(StatisticDto::getValue)
                .filter(value -> !value.isEmpty());
    }

}
