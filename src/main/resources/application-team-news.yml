# Team News Configuration Properties
# This file demonstrates how to configure the Team News functionality

team-news:
  # API Configuration
  api:
    model: "grok-3-latest"
    temperature: 0
    max-tokens: 1500
    max-search-results: 20
    stream: false
    return-citations: true
    search-mode: "on"
    default-x-handles:
      - "UEFA"
      - "FIFAcom"
      - "ChampionsLeague"
    source-types:
      x: "x"
      web: "web"
      news: "news"
    search-date-range-days: 30

  # Content Processing Configuration
  processing:
    pipeline:
      enabled: true
      processor-order:
        - "MarkdownHeaderProcessor"
        - "SpecialSymbolProcessor"
        - "TeamSectionExtractor"
        - "WhitespaceProcessor"
      log-steps: true
    cleaning:
      remove-headers: true
      remove-special-symbols: true
      normalize-whitespace: true
      extract-team-sections: true

  # Strategy Configuration
  strategy:
    default-parsing-strategy: "markdown"
    default-cleaning-strategy: "markdown"
    auto-selection: true
    selection:
      content-types:
        markdown-patterns:
          - "##"
          - "###"
          - "*"
          - "_"
          - "`"
        html-patterns:
          - "<"
          - ">"
          - "&lt;"
          - "&gt;"
        json-patterns:
          - "{"
          - "}"
          - "["
          - "]"
          - "\":" 

  # Monitoring Configuration
  monitoring:
    enabled: true
    log-metrics: true
    track-token-usage: true
    track-processing-times: true
    metrics-interval-seconds: 60

# Spring Boot Actuator Configuration for Health Checks
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,info
  endpoint:
    health:
      show-details: always
  health:
    defaults:
      enabled: true

# Logging Configuration
logging:
  level:
    com.sportal365.articlescheduler.sportsdata.twitter.teamnews: DEBUG
    com.sportal365.articlescheduler.sportsdata.twitter.teamnews.monitoring: INFO
    com.sportal365.articlescheduler.sportsdata.twitter.teamnews.resilience: WARN
